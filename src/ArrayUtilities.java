/**
 * ArrayUtilities class containing static utility methods for 2D array operations
 * Used for calculating row and column totals in the Medal Tally program
 */
public class ArrayUtilities {
    
    /**
     * Calculate the total of a specific column in a 2D integer array
     * @param array The 2D integer array
     * @param columnIndex The index of the column to sum
     * @return The sum of all values in the specified column
     */
    public static int getColumnTotal(int[][] array, int columnIndex) {
        int total = 0;
        
        // Validate input parameters
        if (array == null || columnIndex < 0) {
            return 0;
        }
        
        // Sum all values in the specified column
        for (int row = 0; row < array.length; row++) {
            // Check if the row has enough columns
            if (array[row] != null && columnIndex < array[row].length) {
                total += array[row][columnIndex];
            }
        }
        
        return total;
    }
    
    /**
     * Calculate the total of a specific row in a 2D integer array
     * @param array The 2D integer array
     * @param rowIndex The index of the row to sum
     * @return The sum of all values in the specified row
     */
    public static int getRowTotal(int[][] array, int rowIndex) {
        int total = 0;
        
        // Validate input parameters
        if (array == null || rowIndex < 0 || rowIndex >= array.length) {
            return 0;
        }
        
        // Check if the specified row exists and is not null
        if (array[rowIndex] != null) {
            // Sum all values in the specified row
            for (int col = 0; col < array[rowIndex].length; col++) {
                total += array[rowIndex][col];
            }
        }
        
        return total;
    }
    
    /**
     * Calculate the total of all elements in a 2D integer array
     * @param array The 2D integer array
     * @return The sum of all values in the array
     */
    public static int getTotalSum(int[][] array) {
        int total = 0;
        
        // Validate input parameter
        if (array == null) {
            return 0;
        }
        
        // Sum all values in the entire array
        for (int row = 0; row < array.length; row++) {
            if (array[row] != null) {
                for (int col = 0; col < array[row].length; col++) {
                    total += array[row][col];
                }
            }
        }
        
        return total;
    }
    
    /**
     * Display a 2D integer array in a formatted table
     * @param array The 2D integer array to display
     * @param rowLabels Array of labels for each row (optional, can be null)
     * @param columnLabels Array of labels for each column (optional, can be null)
     */
    public static void displayArray(int[][] array, String[] rowLabels, String[] columnLabels) {
        if (array == null) {
            System.out.println("Array is null");
            return;
        }
        
        // Print column headers if provided
        if (columnLabels != null) {
            System.out.print("\t\t");
            for (String label : columnLabels) {
                System.out.printf("%-8s", label);
            }
            System.out.println();
        }
        
        // Print array contents with row labels if provided
        for (int row = 0; row < array.length; row++) {
            // Print row label if provided
            if (rowLabels != null && row < rowLabels.length) {
                System.out.printf("%-15s\t", rowLabels[row]);
            } else {
                System.out.printf("Row %d:\t\t", row);
            }
            
            // Print row values
            if (array[row] != null) {
                for (int col = 0; col < array[row].length; col++) {
                    System.out.printf("%-8d", array[row][col]);
                }
            }
            System.out.println();
        }
    }
    
    /**
     * Find the maximum value in a 2D integer array
     * @param array The 2D integer array
     * @return The maximum value found in the array
     */
    public static int findMaxValue(int[][] array) {
        if (array == null || array.length == 0) {
            return Integer.MIN_VALUE;
        }
        
        int max = Integer.MIN_VALUE;
        
        for (int row = 0; row < array.length; row++) {
            if (array[row] != null) {
                for (int col = 0; col < array[row].length; col++) {
                    if (array[row][col] > max) {
                        max = array[row][col];
                    }
                }
            }
        }
        
        return max;
    }
    
    /**
     * Find the minimum value in a 2D integer array
     * @param array The 2D integer array
     * @return The minimum value found in the array
     */
    public static int findMinValue(int[][] array) {
        if (array == null || array.length == 0) {
            return Integer.MAX_VALUE;
        }
        
        int min = Integer.MAX_VALUE;
        
        for (int row = 0; row < array.length; row++) {
            if (array[row] != null) {
                for (int col = 0; col < array[row].length; col++) {
                    if (array[row][col] < min) {
                        min = array[row][col];
                    }
                }
            }
        }
        
        return min;
    }
}
