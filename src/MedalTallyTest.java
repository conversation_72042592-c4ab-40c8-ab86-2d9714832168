import java.util.Scanner;

/**
 * Tester class for the Medal Tally program
 * Demonstrates Commonwealth Games medal tracking functionality
 */
public class MedalTallyTest {
    public static void main(String[] args) throws Exception {
        System.out.println("=== COMMONWEALTH GAMES MEDAL TALLY PROGRAM ===\n");

        // Create MedalTally object
        MedalTally medalTally = new MedalTally();

        // Display menu options
        displayMenu();

        Scanner scanner = new Scanner(System.in);
        int choice;

        do {
            System.out.print("\nEnter your choice (1-6): ");
            choice = scanner.nextInt();
            scanner.nextLine(); // Consume newline

            switch (choice) {
                case 1:
                    // Collect medal data from user
                    medalTally.collectMedalData();
                    break;

                case 2:
                    // Display 2D array contents
                    medalTally.displayMedalData();
                    break;

                case 3:
                    // Display column totals (medal type totals)
                    medalTally.displayColumnTotals();
                    break;

                case 4:
                    // Display row totals (country totals)
                    medalTally.displayRowTotals();
                    break;

                case 5:
                    // Display complete medal tally using toString()
                    System.out.println(medalTally.toString());
                    break;

                case 6:
                    // Load sample data for demonstration
                    loadSampleData(medalTally);
                    System.out.println("Sample data loaded successfully!");
                    break;

                case 7:
                    // Test ArrayUtilities methods
                    testArrayUtilities(medalTally);
                    break;

                case 0:
                    System.out.println("Thank you for using the Medal Tally program!");
                    break;

                default:
                    System.out.println("Invalid choice. Please try again.");
            }

            if (choice != 0) {
                displayMenu();
            }

        } while (choice != 0);

        scanner.close();
    }

    /**
     * Display the main menu options
     */
    private static void displayMenu() {
        System.out.println("\n--- MENU OPTIONS ---");
        System.out.println("1. Enter medal data");
        System.out.println("2. Display medal data (2D array)");
        System.out.println("3. Display medal type totals (column totals)");
        System.out.println("4. Display country totals (row totals)");
        System.out.println("5. Display complete medal tally");
        System.out.println("6. Load sample data");
        System.out.println("7. Test ArrayUtilities methods");
        System.out.println("0. Exit");
    }

    /**
     * Load sample data for demonstration purposes
     * @param medalTally The MedalTally object to populate with data
     */
    private static void loadSampleData(MedalTally medalTally) {
        // Sample data for Commonwealth Games
        medalTally.addMedalData(1, 67, 57, 54);  // Australia
        medalTally.addMedalData(2, 45, 45, 46);  // England
        medalTally.addMedalData(3, 26, 32, 34);  // Canada
        medalTally.addMedalData(4, 22, 16, 23);  // India
        medalTally.addMedalData(5, 15, 16, 18);  // New Zealand
        medalTally.addMedalData(6, 13, 11, 13);  // South Africa
        medalTally.addMedalData(7, 11, 13, 22);  // Scotland
        medalTally.addMedalData(8, 8, 6, 14);   // Wales
        medalTally.addMedalData(9, 5, 2, 6);    // Bahamas
        medalTally.addMedalData(10, 6, 8, 2);   // Jamaica
    }

    /**
     * Test ArrayUtilities methods with the current medal data
     * @param medalTally The MedalTally object containing data to test
     */
    private static void testArrayUtilities(MedalTally medalTally) {
        System.out.println("\n=== TESTING ARRAYUTILITIES METHODS ===");

        int[][] data = medalTally.getMedalData();

        // Test column totals
        System.out.println("\nColumn Totals:");
        System.out.println("Gold medals (Column 0): " + ArrayUtilities.getColumnTotal(data, 0));
        System.out.println("Silver medals (Column 1): " + ArrayUtilities.getColumnTotal(data, 1));
        System.out.println("Bronze medals (Column 2): " + ArrayUtilities.getColumnTotal(data, 2));

        // Test row totals for first few countries
        System.out.println("\nRow Totals (Sample):");
        for (int i = 0; i < 5; i++) {
            int total = ArrayUtilities.getRowTotal(data, i);
            if (total > 0) {
                System.out.println("Country " + (i + 1) + " (" + medalTally.getCountryName(i + 1) + "): " + total + " medals");
            }
        }

        // Test total sum
        System.out.println("\nTotal medals across all countries: " + ArrayUtilities.getTotalSum(data));

        // Test max and min values
        System.out.println("Maximum medal count in any category: " + ArrayUtilities.findMaxValue(data));
        System.out.println("Minimum medal count in any category: " + ArrayUtilities.findMinValue(data));

        // Display formatted array
        System.out.println("\nFormatted Array Display:");
        String[] countries = {"Australia", "England", "Canada", "India", "New Zealand"};
        String[] medals = {"Gold", "Silver", "Bronze"};

        // Create a smaller array for display (first 5 countries)
        int[][] displayData = new int[5][3];
        for (int i = 0; i < 5; i++) {
            for (int j = 0; j < 3; j++) {
                displayData[i][j] = data[i][j];
            }
        }

        ArrayUtilities.displayArray(displayData, countries, medals);
    }
}
