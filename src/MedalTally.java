import java.util.Scanner;

/**
 * MedalTally class to manage Commonwealth Games medal data for countries
 * Uses a 2D array to store medal counts (gold, silver, bronze) for each country
 */
public class MedalTally {
    // 2D array to store medal data: [country][medal_type]
    // Column 0: Gold, Column 1: Silver, Column 2: Bronze
    private int[][] medalData;
    
    // 1D array to store country names (prevents hardcoding)
    private String[] countryNames;
    
    // Constants for array dimensions
    private static final int MAX_COUNTRIES = 15;
    private static final int MEDAL_TYPES = 3; // Gold, Silver, Bronze
    private static final int GOLD = 0;
    private static final int SILVER = 1;
    private static final int BRONZE = 2;
    
    /**
     * Constructor to initialize the MedalTally object
     */
    public MedalTally() {
        medalData = new int[MAX_COUNTRIES][MEDAL_TYPES];
        initializeCountryNames();
    }
    
    /**
     * Initialize country names array with 15 Commonwealth countries
     * This prevents hardcoding in the main program
     */
    private void initializeCountryNames() {
        countryNames = new String[MAX_COUNTRIES];
        countryNames[0] = "Australia";
        countryNames[1] = "England";
        countryNames[2] = "Canada";
        countryNames[3] = "India";
        countryNames[4] = "New Zealand";
        countryNames[5] = "South Africa";
        countryNames[6] = "Scotland";
        countryNames[7] = "Wales";
        countryNames[8] = "Bahamas";
        countryNames[9] = "Jamaica";
        countryNames[10] = "Kenya";
        countryNames[11] = "Nigeria";
        countryNames[12] = "Singapore";
        countryNames[13] = "Malaysia";
        countryNames[14] = "Cyprus";
    }
    
    /**
     * Get country name by country code/number
     * @param countryCode The country code (1-15)
     * @return Country name or "Unknown" if invalid code
     */
    public String getCountryName(int countryCode) {
        if (countryCode >= 1 && countryCode <= MAX_COUNTRIES) {
            return countryNames[countryCode - 1]; // Convert to 0-based index
        }
        return "Unknown";
    }
    
    /**
     * Add medal data for a country
     * @param countryCode Country code (1-15)
     * @param gold Number of gold medals
     * @param silver Number of silver medals
     * @param bronze Number of bronze medals
     */
    public void addMedalData(int countryCode, int gold, int silver, int bronze) {
        if (countryCode >= 1 && countryCode <= MAX_COUNTRIES) {
            int index = countryCode - 1; // Convert to 0-based index
            medalData[index][GOLD] = gold;
            medalData[index][SILVER] = silver;
            medalData[index][BRONZE] = bronze;
        }
    }
    
    /**
     * Collect medal data from user input
     */
    public void collectMedalData() {
        Scanner scanner = new Scanner(System.in);
        System.out.println("Enter medal data for Commonwealth Games countries:");
        System.out.println("Format: countryCode,gold,silver,bronze");
        System.out.println("Enter 0 to stop data entry");
        System.out.println("Country codes: 1-15 (max 15 entries)");
        
        int entriesCount = 0;
        
        while (entriesCount < MAX_COUNTRIES) {
            System.out.print("Enter data (or 0 to stop): ");
            String input = scanner.nextLine().trim();
            
            // Check if user wants to stop
            if (input.equals("0")) {
                break;
            }
            
            // Parse the input
            String[] parts = input.split(",");
            if (parts.length == 4) {
                try {
                    int countryCode = Integer.parseInt(parts[0].trim());
                    int gold = Integer.parseInt(parts[1].trim());
                    int silver = Integer.parseInt(parts[2].trim());
                    int bronze = Integer.parseInt(parts[3].trim());
                    
                    if (countryCode >= 1 && countryCode <= MAX_COUNTRIES) {
                        addMedalData(countryCode, gold, silver, bronze);
                        System.out.println("Data added for " + getCountryName(countryCode));
                        entriesCount++;
                    } else {
                        System.out.println("Invalid country code. Please enter 1-15.");
                    }
                } catch (NumberFormatException e) {
                    System.out.println("Invalid input format. Please use: countryCode,gold,silver,bronze");
                }
            } else {
                System.out.println("Invalid input format. Please use: countryCode,gold,silver,bronze");
            }
        }
    }
    
    /**
     * Get the 2D medal data array
     * @return The medal data array
     */
    public int[][] getMedalData() {
        return medalData;
    }
    
    /**
     * Display the contents of the 2D array
     */
    public void displayMedalData() {
        System.out.println("\nMedal Data (2D Array Contents):");
        System.out.println("Country\t\tGold\tSilver\tBronze");
        System.out.println("----------------------------------------");
        
        for (int i = 0; i < MAX_COUNTRIES; i++) {
            if (medalData[i][GOLD] > 0 || medalData[i][SILVER] > 0 || medalData[i][BRONZE] > 0) {
                System.out.printf("%-15s\t%d\t%d\t%d%n", 
                    countryNames[i], 
                    medalData[i][GOLD], 
                    medalData[i][SILVER], 
                    medalData[i][BRONZE]);
            }
        }
    }
    
    /**
     * Display column totals (total of each medal type)
     */
    public void displayColumnTotals() {
        System.out.println("\nTotal medals by type:");
        System.out.println("Gold medals: " + ArrayUtilities.getColumnTotal(medalData, GOLD));
        System.out.println("Silver medals: " + ArrayUtilities.getColumnTotal(medalData, SILVER));
        System.out.println("Bronze medals: " + ArrayUtilities.getColumnTotal(medalData, BRONZE));
    }
    
    /**
     * Display row totals (total medals per country)
     */
    public void displayRowTotals() {
        System.out.println("\nTotal medals by country:");
        for (int i = 0; i < MAX_COUNTRIES; i++) {
            if (medalData[i][GOLD] > 0 || medalData[i][SILVER] > 0 || medalData[i][BRONZE] > 0) {
                int total = ArrayUtilities.getRowTotal(medalData, i);
                System.out.println(countryNames[i] + ": " + total + " medals");
            }
        }
    }
    
    /**
     * toString method to display all medal information
     * @return Formatted string with all medal data
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("\n=== COMMONWEALTH GAMES MEDAL TALLY ===\n\n");
        
        // Display medal data table
        sb.append("Country\t\t\tGold\tSilver\tBronze\tTotal\n");
        sb.append("--------------------------------------------------------\n");
        
        for (int i = 0; i < MAX_COUNTRIES; i++) {
            if (medalData[i][GOLD] > 0 || medalData[i][SILVER] > 0 || medalData[i][BRONZE] > 0) {
                int total = ArrayUtilities.getRowTotal(medalData, i);
                sb.append(String.format("%-20s\t%d\t%d\t%d\t%d%n", 
                    countryNames[i], 
                    medalData[i][GOLD], 
                    medalData[i][SILVER], 
                    medalData[i][BRONZE],
                    total));
            }
        }
        
        // Display totals
        sb.append("--------------------------------------------------------\n");
        sb.append(String.format("TOTALS:\t\t\t%d\t%d\t%d\t%d%n",
            ArrayUtilities.getColumnTotal(medalData, GOLD),
            ArrayUtilities.getColumnTotal(medalData, SILVER),
            ArrayUtilities.getColumnTotal(medalData, BRONZE),
            ArrayUtilities.getColumnTotal(medalData, GOLD) + 
            ArrayUtilities.getColumnTotal(medalData, SILVER) + 
            ArrayUtilities.getColumnTotal(medalData, BRONZE)));
        
        return sb.toString();
    }
}
